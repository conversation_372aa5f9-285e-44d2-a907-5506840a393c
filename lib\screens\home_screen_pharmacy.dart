import 'package:flutter/material.dart';

import 'dart:math';
import 'dart:async';
import 'package:shared_preferences/shared_preferences.dart';
import 'pharmacy_inventory_screen.dart';
import 'pharmacy_reports_screen.dart';
import '../services/order_service.dart';
import '../services/pharmacy_service.dart';
import 'pharmacy_settings_screen.dart';
import 'accepted_orders_screen.dart';

class PharmacyOrdersScreen extends StatefulWidget {
  final bool isDark;
  final VoidCallback onToggleDarkMode;
  final VoidCallback onLogout;

  const PharmacyOrdersScreen({
    Key? key,
    required this.isDark,
    required this.onToggleDarkMode,
    required this.onLogout,
  }) : super(key: key);

  @override
  _PharmacyOrdersScreenState createState() => _PharmacyOrdersScreenState();
}

class _PharmacyOrdersScreenState extends State<PharmacyOrdersScreen>
    with TickerProviderStateMixin {
  late AnimationController _waveController;
  late AnimationController _cardAnimationController;
  late AnimationController _headerAnimationController;
  late AnimationController _fabAnimationController;

  late Animation<double> _waveAnimation;
  late Animation<double> _cardSlideAnimation;
  late Animation<double> _cardFadeAnimation;
  late Animation<double> _headerSlideAnimation;
  late Animation<double> _fabScaleAnimation;

  final _scrollController = ScrollController();
  double _scrollOffset = 0;
  bool _isDarkMode = false;
  int _currentIndex = 0;

  // متغيرات الطلبات الحقيقية
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = false;
  String? _errorMessage;
  String _pharmacyName = 'الصيدلية';

  // الطلبات الوهمية للعرض (سيتم استبدالها)
  final List<Map<String, dynamic>> _dummyOrders = [
    {
      'id': 'ORD-2023-001',
      'customer': 'أحمد محمد',
      'medicines': ['باراسيتامول 500mg', 'فيتامين C 1000mg'],
      'time': 'منذ 5 دقائق',
      'status': 'new',
      'prescription': 'assets/images/prescription1.png',
      'offer': null,
      'avatarColor': Colors.blue,
    },
    {
      'id': 'ORD-2023-002',
      'customer': 'سارة خالد',
      'medicines': ['أموكسيسيلين 500mg', 'مضاد حيوي'],
      'time': 'منذ 15 دقيقة',
      'status': 'pending',
      'prescription': null,
      'offer': 75.50,
      'avatarColor': Colors.pink,
    },
    {
      'id': 'ORD-2023-003',
      'customer': 'علي حسن',
      'medicines': ['فيتامين D3', 'كالسيوم', 'ماغنيسيوم'],
      'time': 'منذ 25 دقيقة',
      'status': 'accepted',
      'prescription': 'assets/images/prescription2.png',
      'offer': 120.0,
      'avatarColor': Colors.green,
    },
    {
      'id': 'ORD-2023-004',
      'customer': 'ليلى عبدالله',
      'medicines': ['أدوية ضغط الدم', 'مدر للبول'],
      'time': 'منذ ساعة',
      'status': 'ready',
      'prescription': 'assets/images/prescription3.png',
      'offer': 65.75,
      'avatarColor': Colors.purple,
    },
  ];

  /// تحميل بيانات الصيدلية
  Future<void> _loadPharmacyData() async {
    try {
      final pharmacyName = await PharmacyService.getPharmacyName();
      if (mounted) {
        setState(() {
          _pharmacyName = pharmacyName;
        });
      }
    } catch (e) {
      print('خطأ في تحميل بيانات الصيدلية: $e');
    }
  }

  /// تحميل الطلبات من الخادم
  Future<void> _loadOrders() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      // التحقق من تسجيل الدخول أولاً
      final token = await PharmacyService.getAccessToken();
      if (token == null) {
        if (mounted) {
          setState(() {
            _errorMessage = 'يجب تسجيل الدخول أولاً';
            _isLoading = false;
            _orders = List<Map<String, dynamic>>.from(_dummyOrders);
          });
        }
        return;
      }

      final result = await OrderService.getPharmacyOrders(
        page: 1,
        limit: 20,
      );

      print('🔍 نتيجة جلب الطلبات: ${result.toString()}');

      if (mounted) {
        if (result['success'] == true) {
          final orders = result['data'] ?? [];
          print('📋 عدد الطلبات المستلمة: ${orders.length}');

          // تنظيف البيانات للتأكد من أن imageUrl هو String
          final cleanedOrders = orders.map<Map<String, dynamic>>((order) {
            final cleanedOrder = Map<String, dynamic>.from(order);

            // تنظيف prescription imageUrl
            if (cleanedOrder['prescription'] != null && cleanedOrder['prescription']['imageUrl'] != null) {
              cleanedOrder['prescription']['imageUrl'] = cleanedOrder['prescription']['imageUrl'].toString();
            }

            // تنظيف medicineRequest prescriptionImageUrl
            if (cleanedOrder['medicineRequest'] != null && cleanedOrder['medicineRequest']['prescriptionImageUrl'] != null) {
              cleanedOrder['medicineRequest']['prescriptionImageUrl'] = cleanedOrder['medicineRequest']['prescriptionImageUrl'].toString();
            }

            return cleanedOrder;
          }).toList();

          setState(() {
            _orders = cleanedOrders;
            _isLoading = false;
          });

          // إذا لم توجد طلبات، استخدم الوهمية للعرض
          if (orders.isEmpty) {
            setState(() {
              _orders = List<Map<String, dynamic>>.from(_dummyOrders);
            });
          }
        } else {
          print('❌ خطأ في الاستجابة: ${result['message']}');
          setState(() {
            _errorMessage = result['message'] ?? 'فشل في تحميل الطلبات';
            _isLoading = false;
            // استخدام الطلبات الوهمية في حالة الفشل
            _orders = List<Map<String, dynamic>>.from(_dummyOrders);
          });
        }
      }
    } catch (e) {
      print('❌ خطأ في تحميل الطلبات: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'خطأ في الاتصال: ${e.toString()}';
          _isLoading = false;
          // استخدام الطلبات الوهمية في حالة الخطأ
          _orders = List<Map<String, dynamic>>.from(_dummyOrders);
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDark;
    _loadPharmacyData();
    _loadOrders();

    // تحديث الطلبات كل 30 ثانية
    Timer.periodic(const Duration(seconds: 30), (timer) {
      if (mounted) {
        _loadOrders();
      } else {
        timer.cancel();
      }
    });
    _loadDarkMode();

    // تهيئة جميع الأنيميشن
    _initializeAnimations();

    // متابعة حركة التمرير
    _scrollController.addListener(() {
      setState(() {
        _scrollOffset = _scrollController.offset;
      });
    });
  }

  void _initializeAnimations() {
    // تحريك الموجة الخلفية
    _waveController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 8),
    )..repeat(reverse: true);

    _waveAnimation = Tween<double>(begin: -20, end: 20).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    // أنيميشن الكروت
    _cardAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _cardSlideAnimation = Tween<double>(begin: 50, end: 0).animate(
      CurvedAnimation(parent: _cardAnimationController, curve: Curves.elasticOut),
    );

    _cardFadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _cardAnimationController, curve: Curves.easeInOut),
    );

    // أنيميشن الهيدر
    _headerAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    _headerSlideAnimation = Tween<double>(begin: -100, end: 0).animate(
      CurvedAnimation(parent: _headerAnimationController, curve: Curves.bounceOut),
    );

    // أنيميشن الـ FAB
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _fabScaleAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.elasticOut),
    );

    // بدء الأنيميشن
    _headerAnimationController.forward();
    _cardAnimationController.forward();

    // تأخير الـ FAB قليلاً
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) _fabAnimationController.forward();
    });
  }

  Future<void> _loadDarkMode() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('pharmacy_dark_mode') ?? widget.isDark;
    });
  }

  Future<void> _toggleDarkMode() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('pharmacy_dark_mode', _isDarkMode);

    // استدعاء الدالة الأصلية إذا كانت موجودة
    widget.onToggleDarkMode();
  }

  @override
  void dispose() {
    _waveController.dispose();
    _cardAnimationController.dispose();
    _headerAnimationController.dispose();
    _fabAnimationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Widget _getCurrentPage() {
    switch (_currentIndex) {
      case 0:
        return _buildOrdersPage();
      case 1:
        return AcceptedOrdersScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 2:
        return PharmacyInventoryScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 3:
        return PharmacyReportsScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
        );
      case 4:
        return PharmacySettingsScreen(
          isDark: _isDarkMode,
          onToggleDarkMode: _toggleDarkMode,
          onLogout: widget.onLogout,
        );
      default:
        return _buildOrdersPage();
    }
  }

  @override
  Widget build(BuildContext context) {
    final bgColor = _isDarkMode ? const Color(0xFF0F0F23) : const Color(0xFFF8FAFC);
    final textColor = _isDarkMode ? Colors.white : const Color(0xFF1E293B);
    const primaryColor = Color(0xFF00BF63);


    return Scaffold(
      backgroundColor: bgColor,
      body: _getCurrentPage(),
      bottomNavigationBar: _buildBottomNavBar(primaryColor, textColor),
      floatingActionButton: _currentIndex == 0 ? AnimatedBuilder(
        animation: _fabAnimationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _fabScaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    primaryColor,
                    primaryColor.withOpacity(0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: primaryColor.withOpacity(0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: primaryColor.withOpacity(0.2),
                    blurRadius: 40,
                    offset: const Offset(0, 16),
                    spreadRadius: -8,
                  ),
                ],
              ),
              child: FloatingActionButton(
                backgroundColor: Colors.transparent,
                elevation: 0,
                onPressed: () {
                  _loadOrders();
                },
                child: _isLoading
                    ? SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                        Icons.refresh,
                        color: Colors.white,
                        size: 28,
                      ),
              ),
            ),
          );
        },
      ) : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }

  Widget _buildOrdersPage() {
    final bgColor = _isDarkMode ? const Color(0xFF0A0E1A) : const Color(0xFFF7F9FC);
    final textColor = _isDarkMode ? const Color(0xFFE2E8F0) : const Color(0xFF1A202C);
    final primaryColor = const Color(0xFF0066FF);
    final accentColor = const Color(0xFF00D4AA);
    final cardColor = _isDarkMode ? const Color(0xFF1A202C) : Colors.white;
    final surfaceColor = _isDarkMode ? const Color(0xFF2D3748) : const Color(0xFFF7F9FC);
    final borderColor = _isDarkMode ? const Color(0xFF4A5568) : const Color(0xFFE2E8F0);

    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.topRight,
          radius: 1.2,
          colors: [
            primaryColor.withOpacity(0.06),
            accentColor.withOpacity(0.03),
            bgColor,
          ],
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: Stack(
        children: [
          // نمط هندسي في الخلفية
          Positioned(
            top: -80,
            right: -80,
            child: AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _waveAnimation.value * 0.1,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          primaryColor.withOpacity(0.08),
                          primaryColor.withOpacity(0.02),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // نمط هندسي آخر
          Positioned(
            bottom: -60,
            left: -60,
            child: AnimatedBuilder(
              animation: _waveAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: -_waveAnimation.value * 0.08,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          accentColor.withOpacity(0.06),
                          accentColor.withOpacity(0.01),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),

          // محتوى الشاشة
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverAppBar(
                expandedHeight: 280,
                floating: false,
                pinned: true,
                backgroundColor: bgColor.withValues(alpha: 0.95),
                elevation: 0,
                leading: Container(),
                actions: [
                  Container(
                    margin: const EdgeInsets.only(right: 20, top: 8, bottom: 8),
                    child: AnimatedBuilder(
                      animation: _fabAnimationController,
                      builder: (context, child) {
                        return Transform.scale(
                          scale: _fabScaleAnimation.value,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [primaryColor, primaryColor.withValues(alpha: 0.8)],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: primaryColor.withValues(alpha: 0.3),
                                  blurRadius: 20,
                                  offset: const Offset(0, 8),
                                  spreadRadius: 0,
                                ),
                                BoxShadow(
                                  color: primaryColor.withValues(alpha: 0.1),
                                  blurRadius: 40,
                                  offset: const Offset(0, 16),
                                  spreadRadius: -8,
                                ),
                              ],
                            ),
                            child: Material(
                              color: Colors.transparent,
                              borderRadius: BorderRadius.circular(16),
                              child: InkWell(
                                borderRadius: BorderRadius.circular(16),
                                onTap: _isLoading ? null : _loadOrders,
                                child: Container(
                                  width: 48,
                                  height: 48,
                                  alignment: Alignment.center,
                                  child: _isLoading
                                      ? const SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                          ),
                                        )
                                      : const Icon(
                                          Icons.refresh_rounded,
                                          color: Colors.white,
                                          size: 24,
                                        ),
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  title: AnimatedOpacity(
                    opacity: _scrollOffset > 120 ? 1.0 : 0.0,
                    duration: const Duration(milliseconds: 200),
                    child: Text(
                      _pharmacyName,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'Tajawal',
                        letterSpacing: 0.5,
                      ),
                    ),
                  ),
                  background: Stack(
                    children: [
                      // خلفية متدرجة حديثة
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              primaryColor.withValues(alpha: 0.08),
                              accentColor.withValues(alpha: 0.05),
                              bgColor,
                            ],
                            stops: const [0.0, 0.6, 1.0],
                          ),
                        ),
                      ),

                      // عناصر هندسية متحركة
                      AnimatedBuilder(
                        animation: _headerAnimationController,
                        builder: (context, child) {
                          return Positioned(
                            top: 40 + _headerSlideAnimation.value * 10,
                            right: 30,
                            child: Transform.rotate(
                              angle: _headerSlideAnimation.value * 0.1,
                              child: Container(
                                width: 80,
                                height: 80,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  gradient: RadialGradient(
                                    colors: [
                                      primaryColor.withValues(alpha: 0.15),
                                      primaryColor.withValues(alpha: 0.05),
                                      Colors.transparent,
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        },
                      ),

                      // محتوى الهيدر
                      Positioned(
                        left: 24,
                        right: 24,
                        top: 60,
                        child: AnimatedBuilder(
                          animation: _headerAnimationController,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(_headerSlideAnimation.value * 20, 0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // اسم الصيدلية
                                  Text(
                                    _pharmacyName,
                                    style: TextStyle(
                                      color: textColor,
                                      fontSize: 28,
                                      fontWeight: FontWeight.w700,
                                      fontFamily: 'Tajawal',
                                      letterSpacing: 0.5,
                                      height: 1.2,
                                    ),
                                  ),
                                  const SizedBox(height: 8),

                                  // وصف مختصر
                                  Text(
                                    'إدارة الطلبات والمخزون',
                                    style: TextStyle(
                                      color: textColor.withValues(alpha: 0.7),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w500,
                                      fontFamily: 'Tajawal',
                                    ),
                                  ),
                                  const SizedBox(height: 20),

                                  // إحصائيات سريعة
                                  Row(
                                    children: [
                                      _buildQuickStat(
                                        'الطلبات اليوم',
                                        '${_orders.length}',
                                        primaryColor,
                                        textColor,
                                      ),
                                      const SizedBox(width: 24),
                                      _buildQuickStat(
                                        'قيد المعالجة',
                                        '${_orders.where((o) => o['status'] == 'pending').length}',
                                        accentColor,
                                        textColor,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // عرض مؤشر التحميل أو الأخطاء أو الطلبات
              ..._buildContentSlivers(textColor, cardColor, primaryColor),
            ],
          ),
        ],
      ),
    );
  }

  // دالة لبناء إحصائية سريعة
  Widget _buildQuickStat(String title, String value, Color color, Color textColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 24,
            fontWeight: FontWeight.w700,
            fontFamily: 'Tajawal',
          ),
        ),
        const SizedBox(height: 4),
        Text(
          title,
          style: TextStyle(
            color: textColor.withValues(alpha: 0.7),
            fontSize: 12,
            fontWeight: FontWeight.w500,
            fontFamily: 'Tajawal',
          ),
        ),
      ],
    );
  }

  List<Widget> _buildContentSlivers(Color textColor, Color cardColor, Color primaryColor) {
    if (_isLoading) {
      return [
        const SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00BF63)),
                ),
                SizedBox(height: 16),
                Text(
                  'جاري تحميل الطلبات...',
                  style: TextStyle(
                    fontFamily: 'Tajawal',
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ];
    } else if (_errorMessage != null) {
      return [
        SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 64,
                  color: Colors.red,
                ),
                SizedBox(height: 16),
                Text(
                  _errorMessage!,
                  style: TextStyle(
                    fontFamily: 'Tajawal',
                    fontSize: 16,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                ElevatedButton(
                  onPressed: _loadOrders,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xFF00BF63),
                    foregroundColor: Colors.white,
                  ),
                  child: Text(
                    'إعادة المحاولة',
                    style: TextStyle(fontFamily: 'Tajawal'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ];
    } else if (_orders.isEmpty) {
      return [
        const SliverFillRemaining(
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 64,
                  color: Colors.grey,
                ),
                SizedBox(height: 16),
                Text(
                  'لا توجد طلبات حالياً',
                  style: TextStyle(
                    fontFamily: 'Tajawal',
                    fontSize: 18,
                    color: Colors.grey,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'ستظهر الطلبات الجديدة هنا',
                  style: TextStyle(
                    fontFamily: 'Tajawal',
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        ),
      ];
    } else {
      return [
        SliverList(
          delegate: SliverChildBuilderDelegate(
            (context, index) {
              final order = _orders[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                child: _buildOrderCard(
                  order,
                  index,
                  textColor,
                  cardColor,
                  primaryColor,
                ),
              );
            },
            childCount: _orders.length,
          ),
        ),
      ];
    }
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
    Color textColor,
    Color bgColor,
    Color accentColor,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: bgColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.bold,
                fontSize: 24,
                fontFamily: 'Tajawal',
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: TextStyle(
                color: textColor.withValues(alpha: 0.7),
                fontSize: 12,
                fontFamily: 'Tajawal',
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderCard(
    Map<String, dynamic> order,
    int index,
    Color textColor,
    Color cardColor,
    Color primaryColor,
  ) {
    final statusInfo = _getStatusInfo(order['status'] ?? 'pending');
    final borderColor = _isDarkMode ? const Color(0xFF4A5568) : const Color(0xFFE2E8F0);
    final shadowColor = _isDarkMode
        ? Colors.black.withValues(alpha: 0.3)
        : Colors.grey.withValues(alpha: 0.1);

    return AnimatedBuilder(
      animation: Listenable.merge([_waveController, _cardAnimationController]),
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            _cardSlideAnimation.value,
            sin(_waveController.value * 2 * pi + index * 0.5) * 2,
          ),
          child: Opacity(
            opacity: _cardFadeAnimation.value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        child: Material(
          borderRadius: BorderRadius.circular(20),
          color: Colors.transparent,
          elevation: 0,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              color: cardColor,
              border: Border.all(
                color: borderColor,
                width: 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: primaryColor.withValues(alpha: 0.08),
                  blurRadius: 24,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: shadowColor,
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                  spreadRadius: -2,
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(20),
              child: InkWell(
                borderRadius: BorderRadius.circular(20),
                onTap: () => _showOrderDetails(order),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Row
                      Row(
                        children: [
                          // Modern Avatar
                          Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  primaryColor,
                                  primaryColor.withValues(alpha: 0.8),
                                ],
                              ),
                            ),
                            child: Center(
                              child: Text(
                                (order['contactInfo']?['name'] ?? 'م').toString().substring(0, 1),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 18,
                                  fontFamily: 'Tajawal',
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),

                          // Customer Info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  order['contactInfo']?['name'] ?? 'مستخدم غير محدد',
                                  style: TextStyle(
                                    color: textColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                    fontFamily: 'Tajawal',
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  order['time'] ?? 'منذ قليل',
                                  style: TextStyle(
                                    color: textColor.withValues(alpha: 0.6),
                                    fontSize: 12,
                                    fontFamily: 'Tajawal',
                                  ),
                                ),
                              ],
                            ),
                          ),

                          // Modern Status Badge
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: statusInfo.color.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: statusInfo.color.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  statusInfo.icon,
                                  size: 16,
                                  color: statusInfo.color,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  statusInfo.text,
                                  style: TextStyle(
                                    color: statusInfo.color,
                                    fontFamily: 'Tajawal',
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Medicine Information
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: primaryColor.withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: borderColor,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.medication,
                              color: primaryColor,
                              size: 18,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تفاصيل الطلب',
                              style: TextStyle(
                                color: textColor,
                                fontWeight: FontWeight.w600,
                                fontSize: 14,
                                fontFamily: 'Tajawal',
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          order['medicineRequest']?['name'] ?? 'دواء غير محدد',
                          style: TextStyle(
                            color: textColor,
                            fontWeight: FontWeight.w500,
                            fontSize: 14,
                            fontFamily: 'Tajawal',
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Text(
                                'الكمية: ${order['medicineRequest']?['quantity'] ?? 0}',
                                style: TextStyle(
                                  color: primaryColor,
                                  fontSize: 11,
                                  fontWeight: FontWeight.w600,
                                  fontFamily: 'Tajawal',
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (order['medicineRequest']?['description'] != null) ...[
                          const SizedBox(height: 8),
                          Text(
                            order['medicineRequest']['description'],
                            style: TextStyle(
                              color: textColor.withValues(alpha: 0.7),
                              fontSize: 13,
                              fontFamily: 'Tajawal',
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  if ((order['medicineRequest']?['prescriptionImageUrl'] != null &&
                       order['medicineRequest']['prescriptionImageUrl'].toString().isNotEmpty) ||
                      (order['prescription']?['imageUrl'] != null &&
                       order['prescription']['imageUrl'].toString().isNotEmpty)) ...[
                    const SizedBox(height: 12),
                    GestureDetector(
                      onTap: () => _showPrescriptionImage(
                        order['medicineRequest']?['prescriptionImageUrl'] ??
                        order['prescription']?['imageUrl']
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Container(
                          height: 80,
                          width: double.infinity,
                          color: Colors.grey[300],
                          child: const Center(
                            child: Icon(Icons.image, color: Colors.grey, size: 32),
                          ),
                        ),
                      ),
                    ),
                  ],

                  const SizedBox(height: 16),

                  // Action Buttons
                  if (order['status'] == 'pending') ...[
                    Row(
                      children: [
                        Expanded(
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(10),
                              onTap: () => _acceptOrder(order['_id']),
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  color: const Color(0xFF10B981),
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0xFF10B981).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.check_circle,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'قبول',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontFamily: 'Tajawal',
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              borderRadius: BorderRadius.circular(10),
                              onTap: () => _showOfferDialog(order),
                              child: Container(
                                height: 44,
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF59E0B),
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: [
                                    BoxShadow(
                                      color: const Color(0xFFF59E0B).withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: const Center(
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.local_offer,
                                        color: Colors.white,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'عرض سعر',
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontFamily: 'Tajawal',
                                          fontWeight: FontWeight.w600,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _showOrderDetails(Map<String, dynamic> order) {
    // عرض تفاصيل الطلب
    print('عرض تفاصيل الطلب: ${order['_id']}');
  }

  void _acceptOrder(String orderId) {
    // قبول الطلب
    print('قبول الطلب: $orderId');
  }

  void _showOfferDialog(Map<String, dynamic> order) {
    // عرض نافذة تقديم العرض
    print('تقديم عرض للطلب: ${order['_id']}');
  }

  void _showPrescriptionImage(String? imageUrl) {
    // عرض صورة الوصفة
    if (imageUrl != null) {
      print('عرض صورة الوصفة: $imageUrl');
    }
  }

  _StatusInfo _getStatusInfo(String status) {
    switch (status) {
      case 'pending':
        return _StatusInfo(
          const Color(0xFFF59E0B),
          Icons.pending,
          'في الانتظار',
        );
      case 'accepted':
        return _StatusInfo(
          const Color(0xFF10B981),
          Icons.check_circle,
          'مقبول',
        );
      case 'rejected':
        return _StatusInfo(
          const Color(0xFFEF4444),
          Icons.cancel,
          'مرفوض',
        );
      default:
        return _StatusInfo(
          const Color(0xFF6B7280),
          Icons.help,
          'غير محدد',
        );
    }
  }



  Widget _buildBottomNavBar(Color primaryColor, Color textColor) {
    return Container(
      decoration: BoxDecoration(
        color: _isDarkMode ? const Color(0xFF1E1E1E) : Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: BottomNavigationBar(
        currentIndex: _currentIndex.clamp(0, 4), // تأكد من أن القيمة ضمن النطاق الصحيح
        onTap: (index) {
          if (index >= 0 && index <= 4) { // تحقق من صحة الفهرس
            setState(() => _currentIndex = index);
          }
        },
        backgroundColor: Colors.transparent,
        selectedItemColor: primaryColor,
        unselectedItemColor: textColor.withOpacity(0.5),
        type: BottomNavigationBarType.fixed,
        elevation: 0,
        selectedLabelStyle: const TextStyle(
          fontFamily: 'Tajawal',
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
        unselectedLabelStyle: const TextStyle(
          fontFamily: 'Tajawal',
          fontWeight: FontWeight.w500,
          fontSize: 11,
        ),
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.assignment),
            label: 'الطلبات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.assignment_turned_in),
            label: 'المقبولة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.inventory),
            label: 'المخزون',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.bar_chart),
            label: 'التقارير',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.settings),
            label: 'الإعدادات',
          ),
        ],
      ),
    );
  }
}

class _StatusInfo {
  final Color color;
  final IconData icon;
  final String text;

  _StatusInfo(this.color, this.icon, this.text);
}
